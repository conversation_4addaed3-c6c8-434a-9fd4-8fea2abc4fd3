# VR项目技术架构设计

本文档详细描述了VR视频分析系统的技术架构设计，包括系统架构、技术选型、接口设计等关键内容。

## 系统架构概览

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        客户端层                              │
│  Web界面 │ API客户端 │ 移动端 │ 第三方系统集成                │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        API网关层                             │
│  FastAPI │ 路由管理 │ 认证授权 │ 限流控制 │ 请求验证          │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        服务层                               │
│  分析服务 │ 视频流服务 │ 配置服务 │ 监控服务                  │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        处理器层                              │
│  水位识别 │ 流速识别 │ 异常检测 │ 图像预处理                  │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        基础设施层                            │
│  配置管理 │ 日志系统 │ 文件存储 │ 缓存系统 │ 监控告警          │
└─────────────────────────────────────────────────────────────┘
```

## 技术选型

### 核心技术栈
| 组件类别 | 技术选择 | 版本要求 | 选择理由 |
|---------|---------|---------|---------|
| Web框架 | FastAPI | 0.104+ | 高性能异步框架，自动API文档生成 |
| Python版本 | Python | 3.10+ | 现代Python特性支持，类型注解 |
| ASGI服务器 | Uvicorn | 0.24+ | 高性能ASGI服务器 |
| 计算机视觉 | OpenCV | 4.8+ | 成熟的图像视频处理库 |
| 数值计算 | NumPy | 1.24+ | 高效的数组计算 |
| 配置管理 | YAML + Pydantic | - | 人类可读配置 + 类型验证 |
| 日志系统 | Python logging | - | 标准库，功能完善 |
| 测试框架 | pytest | 7.0+ | 功能强大的测试框架 |

### 专业算法库
| 功能模块 | 技术选择 | 用途 |
|---------|---------|------|
| 流速识别 | OpenPIV | 粒子图像测速算法 |
| 异常检测 | YOLO/Ultralytics | 目标检测和识别 |
| 图像处理 | scikit-image | 高级图像处理算法 |
| 数据分析 | pandas | 结果数据处理和分析 |

## 模块设计

### 1. API接口层 (`src/api/`)

#### 核心接口设计
```python
# 分析接口
POST /api/analyze/water-level    # 水位识别
POST /api/analyze/flow-speed     # 流速识别  
POST /api/analyze/anomaly        # 异常检测
POST /api/analyze/batch          # 批量分析

# 视频流接口
POST /api/stream/start           # 启动流分析
POST /api/stream/stop            # 停止流分析
GET  /api/stream/status          # 查询流状态

# 配置管理接口
GET  /api/config                 # 获取配置
PUT  /api/config                 # 更新配置
POST /api/config/reload          # 重载配置

# 系统接口
GET  /api/health                 # 健康检查
GET  /api/metrics                # 系统指标
```

#### 请求/响应模型
```python
# 分析请求模型
class AnalyzeRequest(BaseModel):
    image_data: str              # Base64编码的图像数据
    analysis_type: str           # 分析类型
    parameters: Dict[str, Any]   # 分析参数
    callback_url: Optional[str]  # 回调地址

# 分析响应模型
class AnalyzeResponse(BaseModel):
    task_id: str                 # 任务ID
    status: str                  # 处理状态
    result: Optional[Dict]       # 分析结果
    confidence: float            # 置信度
    processing_time: float       # 处理时间
    error_message: Optional[str] # 错误信息
```

### 2. 服务层 (`src/services/`)

#### 分析服务 (`analyzer_service.py`)
```python
class AnalyzerService:
    """分析服务协调器"""
    
    async def analyze_image(self, request: AnalyzeRequest) -> AnalyzeResponse:
        """图像分析入口"""
        
    async def analyze_video_stream(self, stream_config: StreamConfig) -> str:
        """视频流分析入口"""
        
    async def get_analysis_result(self, task_id: str) -> AnalyzeResponse:
        """获取分析结果"""
```

#### 视频流服务 (`stream_service.py`)
```python
class StreamService:
    """视频流管理服务"""
    
    async def start_stream_analysis(self, config: StreamConfig) -> str:
        """启动视频流分析"""
        
    async def stop_stream_analysis(self, stream_id: str) -> bool:
        """停止视频流分析"""
        
    async def get_stream_status(self, stream_id: str) -> StreamStatus:
        """获取流状态"""
```

### 3. 处理器层 (`src/processors/`)

#### 水位识别处理器 (`water_level/`)
```python
class WaterLevelProcessor:
    """水位识别处理器"""
    
    def __init__(self, config: WaterLevelConfig):
        self.config = config
        self.algorithms = {
            'transparency': TransparencyDetector(),
            'gradient': GradientDetector(),
            'hough': HoughDetector(),
            'color_threshold': ColorThresholdDetector()
        }
    
    async def process(self, image: np.ndarray) -> WaterLevelResult:
        """执行水位识别"""
        
    def detect_water_rule(self, image: np.ndarray) -> RuleDetectionResult:
        """检测水尺"""
        
    def calculate_water_depth(self, surface_line: int, rule_info: dict) -> float:
        """计算水位深度"""
```

#### 流速识别处理器 (`flow_speed/`)
```python
class FlowSpeedProcessor:
    """流速识别处理器"""
    
    def __init__(self, config: FlowSpeedConfig):
        self.config = config
        self.algorithms = {
            'otv': OTVAnalyzer(),
            'piv': PIVAnalyzer(),
            'adaptive_piv': AdaptivePIVAnalyzer()
        }
    
    async def process(self, video_frames: List[np.ndarray]) -> FlowSpeedResult:
        """执行流速识别"""
        
    def preprocess_video(self, video_path: str) -> List[np.ndarray]:
        """视频预处理"""
```

#### 异常检测处理器 (`anomaly/`)
```python
class AnomalyProcessor:
    """异常检测处理器"""
    
    def __init__(self, config: AnomalyConfig):
        self.config = config
        self.yolo_model = YOLOModel(config.model_path)
        
    async def process(self, image: np.ndarray) -> AnomalyResult:
        """执行异常检测"""
        
    def detect_objects(self, image: np.ndarray) -> List[Detection]:
        """目标检测"""
        
    def analyze_behavior(self, detections: List[Detection]) -> List[Anomaly]:
        """行为分析"""
```

### 4. 配置管理 (`src/config/`)

#### 配置结构设计
```yaml
# app_config.yaml
server:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  debug: false

analyzers:
  water_level:
    enabled: true
    default_method: "transparency"
    methods:
      transparency:
        scheme: "contrast_change"
        threshold: 0.5
      gradient:
        grad_thresh: 25
        kernel_size: 3
      hough:
        thresh: 70
        min_line_length: 50
      color_threshold:
        hsv_lower: [35, 40, 40]
        hsv_upper: [85, 255, 255]
        
  flow_speed:
    enabled: true
    default_method: "otv"
    methods:
      otv:
        feature_params:
          maxCorners: 100
          qualityLevel: 0.3
          minDistance: 7
        lk_params:
          winSize: [15, 15]
          maxLevel: 2
      piv:
        window_size: 32
        overlap: 16
        search_area_size: 64
        
  anomaly:
    enabled: true
    model_path: "models/yolo11n.pt"
    confidence_threshold: 0.5
    targets:
      - "person"
      - "boat"
      - "vehicle"

video_streams:
  max_concurrent: 10
  buffer_duration: 30
  frame_rate: 30
  supported_formats: ["rtmp", "rtsp", "http"]

storage:
  results_path: "data/results"
  temp_path: "data/temp"
  max_file_size: "100MB"
  retention_days: 30

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/app.log"
  max_file_size: "10MB"
  backup_count: 5
```

## 数据流设计

### 图像分析数据流
```
客户端请求 → API验证 → 服务层路由 → 处理器选择 → 算法执行 → 结果封装 → 响应返回
     ↓           ↓         ↓          ↓         ↓         ↓         ↓
   请求数据 → 参数验证 → 任务创建 → 图像预处理 → AI分析 → 结果处理 → JSON响应
```

### 视频流分析数据流
```
视频流输入 → 帧提取 → 缓冲队列 → 异步处理 → 结果聚合 → 事件触发 → 结果存储
     ↓         ↓       ↓        ↓        ↓        ↓        ↓
   RTMP/RTSP → 解码器 → 帧缓存 → 多线程处理 → 结果合并 → 回调通知 → 数据库/文件
```

## 性能设计

### 并发处理策略
1. **异步IO**: 使用asyncio处理网络IO和文件IO
2. **多线程**: CPU密集型算法使用线程池
3. **进程池**: 大型模型推理使用进程池
4. **队列管理**: 使用队列缓冲处理任务

### 内存管理
1. **图像缓存**: 限制内存中图像数量
2. **模型缓存**: 延迟加载和卸载AI模型
3. **结果缓存**: 使用LRU缓存常用结果
4. **垃圾回收**: 定期清理临时文件

### 性能指标
- **响应时间**: 单图像分析 < 5秒
- **并发能力**: 支持10个并发视频流
- **内存使用**: 峰值内存 < 4GB
- **CPU使用**: 平均CPU使用率 < 80%

## 安全设计

### API安全
1. **认证授权**: API Key或JWT Token
2. **请求限流**: 防止API滥用
3. **输入验证**: 严格的参数验证
4. **错误处理**: 不泄露敏感信息

### 数据安全
1. **数据加密**: 敏感数据加密存储
2. **访问控制**: 基于角色的访问控制
3. **审计日志**: 记录所有操作日志
4. **数据清理**: 定期清理临时数据

## 监控和运维

### 健康检查
```python
@app.get("/api/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "version": "1.0.0",
        "services": {
            "water_level": "active",
            "flow_speed": "active", 
            "anomaly": "active"
        }
    }
```

### 指标监控
- **系统指标**: CPU、内存、磁盘使用率
- **业务指标**: 分析任务数、成功率、平均处理时间
- **错误指标**: 错误率、异常类型统计
- **性能指标**: 响应时间分布、吞吐量

### 日志管理
```python
# 结构化日志格式
{
    "timestamp": "2024-01-01T12:00:00Z",
    "level": "INFO",
    "service": "water_level",
    "task_id": "task_123",
    "message": "Water level analysis completed",
    "duration": 2.5,
    "result": {"depth": 1.25, "confidence": 0.95}
}
```

## 部署架构

### 容器化部署
```dockerfile
FROM python:3.10-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY src/ ./src/
COPY config/ ./config/
COPY main.py .

EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 扩展性设计
1. **水平扩展**: 支持多实例部署
2. **负载均衡**: 使用Nginx或云负载均衡
3. **服务发现**: 支持服务注册和发现
4. **配置中心**: 集中化配置管理

## 测试策略

### 测试层次
1. **单元测试**: 测试各个组件功能
2. **集成测试**: 测试模块间集成
3. **API测试**: 测试接口功能和性能
4. **端到端测试**: 测试完整业务流程

### 测试覆盖率目标
- **代码覆盖率**: > 80%
- **分支覆盖率**: > 70%
- **API覆盖率**: 100%
- **关键路径覆盖率**: 100%
