# VR项目技术设计文档

本文档详细描述VR视频分析系统的技术架构、API设计和可复用组件分析。

## 1. 系统架构设计

### 1.1 整体架构

```
外部调用系统 → FastAPI服务 → 分析处理器 → 算法引擎
     ↓            ↓           ↓          ↓
  视频流URL → API接口验证 → 视频流处理 → 分析结果
```

**设计原则**：
- 单体应用架构，不是微服务
- 专注视频分析，不管理数据库
- 纯API服务，不包含客户端
- 所有输入都是视频流URL

### 1.2 技术栈选择

| 组件 | 技术选择 | 理由 |
|------|---------|------|
| Web框架 | FastAPI | 高性能异步框架，自动API文档 |
| Python版本 | 3.10+ | 现代特性支持，类型注解 |
| 视觉处理 | OpenCV | 成熟的图像视频处理库 |
| 数值计算 | NumPy | 高效数组计算 |
| 流速算法 | OpenPIV | 专业PIV算法库 |
| 异常检测 | YOLO | 成熟的目标检测模型 |
| 配置管理 | YAML + Pydantic | 人类可读 + 类型验证 |
| 测试框架 | pytest | 功能强大的测试工具 |

### 1.3 详细模块设计

```
src/
├── api/                           # API接口层
│   ├── __init__.py
│   ├── analyze.py                # 分析接口路由
│   └── health.py                 # 健康检查接口
├── services/                     # 业务服务层
│   ├── __init__.py
│   ├── analyzer_service.py       # 分析服务协调器
│   ├── task_scheduler.py         # 任务调度器
│   └── stream_manager.py         # 视频流管理器
├── processors/                   # 分析处理器层
│   ├── __init__.py
│   ├── base_processor.py         # 处理器基类
│   ├── processor_pool.py         # 处理器池管理
│   ├── water_level/              # 水位识别模块
│   │   ├── __init__.py
│   │   ├── processor.py          # 水位识别处理器
│   │   ├── calculator.py         # 水位计算引擎
│   │   ├── algorithms/           # 检测算法
│   │   │   ├── transparency_detector.py
│   │   │   ├── gradient_detector.py
│   │   │   ├── hough_detector.py
│   │   │   └── color_detector.py
│   │   └── calibration/          # 标定功能
│   │       ├── ruler_detector.py
│   │       └── shape_recognizer.py
│   ├── flow_speed/               # 流速识别模块
│   │   ├── __init__.py
│   │   ├── processor.py          # 流速识别处理器
│   │   └── algorithms/           # 分析算法
│   │       ├── otv_analyzer.py
│   │       └── piv_analyzer.py
│   └── anomaly/                  # 异常检测模块
│       ├── __init__.py
│       ├── processor.py          # 异常检测处理器
│       └── detector.py           # YOLO检测器
├── models/                       # 数据模型层
│   ├── __init__.py
│   └── schemas.py                # API数据模型
├── utils/                        # 工具层
│   ├── __init__.py
│   ├── video_utils.py            # 视频处理工具
│   ├── image_utils.py            # 图像处理工具
│   ├── frame_buffer.py           # 帧缓冲管理
│   └── logger.py                 # 日志管理
└── config/                       # 配置层
    ├── __init__.py
    └── config_manager.py         # 配置管理器
```

## 2. API接口设计

### 2.1 核心分析接口

#### 水位识别接口
```
POST /api/analyze/water-level
```

**请求格式**：
```json
{
    "video_url": "rtmp://example.com/stream",
    "parameters": {
        "method": "transparency",           // 检测方法
        "analysis_duration": 30,           // 分析时长(秒)
        "roi": {                          // 感兴趣区域(可选)
            "x": 100, "y": 100, "width": 500, "height": 300
        }
    }
}
```

**响应格式**：
```json
{
    "task_id": "wl_20240101_123456",
    "status": "completed",
    "result": {
        "water_level": {
            "depth_cm": 125.5,
            "confidence": 0.95
        },
        "method_used": "transparency",
        "processing_time": 2.5
    },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 流速识别接口
```
POST /api/analyze/flow-speed
```

**请求格式**：
```json
{
    "video_url": "rtmp://example.com/stream",
    "parameters": {
        "method": "otv",                   // 分析方法: otv/piv
        "analysis_duration": 60,          // 分析时长(秒)
        "roi": {                          // 分析区域
            "points": [                   // 多边形顶点
                {"x": 100, "y": 100},
                {"x": 400, "y": 100},
                {"x": 400, "y": 300},
                {"x": 100, "y": 300}
            ]
        },
        "calibration": {
            "pixel_to_meter": 0.01        // 像素到米转换比例
        }
    }
}
```

**响应格式**：
```json
{
    "task_id": "fs_20240101_123456",
    "status": "completed",
    "result": {
        "flow_speed": {
            "average_speed_ms": 0.85,
            "max_speed_ms": 1.2,
            "flow_direction": 92.5,
            "confidence": 0.88
        },
        "method_used": "otv",
        "processing_time": 45.2
    },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 异常检测接口
```
POST /api/analyze/anomaly
```

**请求格式**：
```json
{
    "video_url": "rtmp://example.com/stream",
    "parameters": {
        "detection_targets": ["person", "boat", "vehicle"],
        "analysis_duration": 30,
        "confidence_threshold": 0.5
    }
}
```

**响应格式**：
```json
{
    "task_id": "ad_20240101_123456",
    "status": "completed",
    "result": {
        "anomalies_detected": true,
        "detections": [
            {
                "object_type": "person",
                "confidence": 0.92,
                "bounding_box": {"x": 150, "y": 200, "width": 80, "height": 180},
                "timestamp": "2024-01-01T12:00:15Z"
            }
        ],
        "risk_level": "medium"
    },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### 2.2 系统接口

#### 健康检查
```
GET /api/health
```

**响应格式**：
```json
{
    "status": "healthy",
    "version": "1.0.0",
    "services": {
        "water_level": "active",
        "flow_speed": "active",
        "anomaly": "active"
    },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 配置查询
```
GET /api/config
```

**响应格式**：
```json
{
    "analyzers": {
        "water_level": {
            "enabled": true,
            "default_method": "transparency",
            "available_methods": ["transparency", "gradient", "hough", "color_threshold"]
        },
        "flow_speed": {
            "enabled": true,
            "default_method": "otv",
            "available_methods": ["otv", "piv"]
        },
        "anomaly": {
            "enabled": true,
            "model_path": "models/yolo11n.pt",
            "supported_targets": ["person", "boat", "vehicle"]
        }
    }
}
```

### 2.3 错误处理

**错误响应格式**：
```json
{
    "error": {
        "code": "INVALID_VIDEO_URL",
        "message": "无法访问指定的视频流URL",
        "details": {
            "url": "rtmp://example.com/stream",
            "error_type": "connection_timeout"
        }
    },
    "task_id": "wl_20240101_123456",
    "timestamp": "2024-01-01T12:00:00Z"
}
```

**常见错误码**：
- `INVALID_VIDEO_URL` - 视频流URL无效
- `VIDEO_STREAM_UNAVAILABLE` - 视频流不可访问
- `ANALYSIS_TIMEOUT` - 分析超时
- `INVALID_PARAMETERS` - 参数无效
- `INTERNAL_ERROR` - 内部错误

## 3. 可复用组件分析

### 3.1 水位识别项目组件

#### 配置管理系统 ⭐⭐⭐⭐⭐
**源文件**: `old_src/water_level/src/config/config_utils.py`
**目标模块**: `src/config/config_manager.py`
**复用方式**: 直接移植ConfigManager类
**具体内容**:
- ConfigManager单例模式配置管理器
- YAML配置文件加载和验证机制
- 运行时配置更新功能

#### 水位检测算法库 ⭐⭐⭐⭐⭐
**源文件**: `old_src/water_level/src/utils/`
**目标模块**: `src/processors/water_level/algorithms/`
**复用方式**: 完整移植并适配新架构

1. **透明度检测算法**
   - 源文件: `transparency_utils.py`
   - 目标: `src/processors/water_level/algorithms/transparency_detector.py`
   - 核心函数: `waterline_transparency_local()`

2. **梯度检测算法**
   - 源文件: `gradient_utils.py`
   - 目标: `src/processors/water_level/algorithms/gradient_detector.py`
   - 核心函数: `waterline_grad_local()`

3. **霍夫变换算法**
   - 源文件: `hough_utils.py`
   - 目标: `src/processors/water_level/algorithms/hough_detector.py`
   - 核心函数: `waterline_hough_local()`

4. **颜色阈值算法**
   - 源文件: `color_threshold_utils.py`
   - 目标: `src/processors/water_level/algorithms/color_detector.py`
   - 核心函数: `waterline_color_threshold_local()`

#### 图像预处理工具 ⭐⭐⭐⭐⭐
**源文件**: `old_src/water_level/src/utils/image_preprocessing_utils.py`
**目标模块**: `src/utils/image_utils.py`
**复用方式**: 整合到通用图像处理工具中
**核心函数**: `_preprocess_roi_for_waterline()`

#### 水尺识别系统 ⭐⭐⭐⭐
**源文件**: `old_src/water_level/src/utils/`
**目标模块**: `src/processors/water_level/calibration/`
**复用方式**: 移植并优化

1. **水尺检测模块**
   - 源文件: `water_rule_utils.py`
   - 目标: `src/processors/water_level/calibration/ruler_detector.py`
   - 核心函数: `detect_water_rule()`, `calculate_pixel_cm_ratio()`

2. **形状识别模块**
   - 源文件: `shape_recognition_utils.py`
   - 目标: `src/processors/water_level/calibration/shape_recognizer.py`
   - 核心函数: `detect_e_shapes()`

#### 水位计算引擎 ⭐⭐⭐⭐⭐
**源文件**: `old_src/water_level/src/utils/water_level.py`
**目标模块**: `src/processors/water_level/calculator.py`
**复用方式**: 作为水位处理器的核心引擎
**核心函数**: `calculate_water_depth()`, `detect_water_surface()`

### 3.2 流速识别项目组件

#### 视频处理工具 ⭐⭐⭐⭐
**源文件**: `old_src/flowspeed/src/utils/video_trimmer.py`
**目标模块**: `src/utils/video_utils.py`
**复用方式**: 整合到通用视频处理工具中
**核心功能**:
- `trim_video()` - 视频时间段裁剪功能
- `sort_polygon_points_clockwise()` - 多边形ROI点排序
- `time_to_seconds()` - 时间格式转换

#### 日志管理系统 ⭐⭐⭐⭐
**源文件**: `old_src/flowspeed/src/utils/logging_utils.py`
**目标模块**: `src/utils/logger.py`
**复用方式**: 作为统一日志系统的基础
**核心功能**: 统一日志配置、多级别输出、文件轮转

#### OTV算法模块 ⭐⭐⭐
**源文件**: `old_src/flowspeed/src/analysis_algorithms/opencv_otv_analyzer.py`
**目标模块**: `src/processors/flow_speed/algorithms/otv_analyzer.py`
**复用方式**: 重构和优化后移植
**核心函数**:
- `analyze_video_segment_with_otv()` - 主分析接口
- `process_video_real_mode()` - 实际OTV处理
- `calculate_angle()` - 流向角度计算
- `filter_by_quartile()` - 异常值过滤

#### PIV算法模块 ⭐⭐⭐
**源文件**: `old_src/flowspeed/src/analysis_algorithms/openpiv_piv_analyzer.py`
**目标模块**: `src/processors/flow_speed/algorithms/piv_analyzer.py`
**复用方式**: 重构和优化后移植
**核心函数**:
- `analyze_video_segment_with_openpiv()` - 主分析接口
- `_perform_piv_for_frame_pair()` - 帧对PIV处理

#### 配置调整工具 ⭐⭐⭐
**源文件**: `old_src/flowspeed/src/utils/adjust_config.py`
**目标模块**: 整合到`src/config/config_manager.py`
**复用方式**: 配置参数动态调整功能

### 3.3 CV异常检测项目组件

#### FastAPI应用架构 ⭐⭐⭐⭐⭐
**源文件**: `old_src/CV/main.py`, `old_src/CV/app/api/`
**目标模块**: `main.py`, `src/api/`
**复用方式**: 作为整个应用的基础架构
**核心组件**:
- FastAPI应用实例创建和配置
- 中间件配置（CORS等）
- 生命周期管理（startup/shutdown）
- 路由注册机制

#### YOLO模型集成 ⭐⭐⭐⭐⭐
**源文件**: `old_src/CV/app/models/yolo_model.py`
**目标模块**: `src/processors/anomaly/detector.py`
**复用方式**: 适配异常检测需求
**核心功能**:
- `YOLOModel` 类 - 模型封装
- `detect()` - 单图像检测
- `detect_multiple()` - 批量检测
- `reload_model()` - 模型热重载

#### 异步处理框架 ⭐⭐⭐⭐
**源文件**: `old_src/CV/app/processor/stream_processor.py`
**目标模块**: `src/services/stream_manager.py`
**复用方式**: 用于视频流的异步处理
**核心组件**:
- `StreamProcessor` 类 - 流处理器
- 异步任务管理
- 帧提取和缓冲

#### 帧缓冲管理 ⭐⭐⭐⭐
**源文件**: `old_src/CV/app/utils/frame_buffer.py`
**目标模块**: `src/utils/frame_buffer.py`
**复用方式**: 直接移植用于视频帧管理
**核心功能**:
- `FrameBuffer` 类 - 帧缓冲器
- 线程安全的帧存储
- 固定大小缓冲区管理

#### 图像处理器 ⭐⭐⭐⭐
**源文件**: `old_src/CV/app/processor/image_processor.py`
**目标模块**: 整合到`src/utils/image_utils.py`
**复用方式**: 图像预处理功能
**核心功能**: 图像格式转换、尺寸调整、预处理

#### 配置管理 ⭐⭐⭐
**源文件**: `old_src/CV/app/utils/config.py`
**目标模块**: 整合到`src/config/config_manager.py`
**复用方式**: 配置加载和验证机制
**核心功能**: YAML配置加载、环境变量支持

### 3.4 整合优先级

**第一优先级**：
1. FastAPI应用架构 (CV项目)
2. 配置管理系统 (水位项目)
3. 水位检测算法 (水位项目)

**第二优先级**：
1. 视频处理工具 (流速项目)
2. YOLO异常检测 (CV项目)
3. 日志管理系统 (流速项目)

**第三优先级**：
1. 流速识别算法 (流速项目，需优化)
2. 异步处理框架 (CV项目)

## 4. 并发处理设计

### 4.1 并发场景分析

#### 场景1: 同一视频流多种分析
```
视频流A → 分析任务1(水位) → 结果1
       → 分析任务2(流速) → 结果2
       → 分析任务3(异常) → 结果3
```

#### 场景2: 多视频流不同分析
```
视频流A → 分析任务1(水位+流速) → 结果1
视频流B → 分析任务2(异常检测) → 结果2
视频流C → 分析任务3(水位) → 结果3
```

### 4.2 并发处理架构

```
API请求 → 任务调度器 → 视频流管理器 → 帧提取器 → 分析处理器
   ↓         ↓           ↓            ↓         ↓
任务队列 → 资源分配 → 流连接池 → 帧缓冲池 → 算法执行池
```

#### 核心组件设计

1. **任务调度器** (`src/services/task_scheduler.py`)
   - 管理所有分析任务
   - 资源分配和负载均衡
   - 任务优先级管理

2. **视频流管理器** (`src/services/stream_manager.py`)
   - 维护视频流连接池
   - 同一流的多任务共享
   - 连接状态监控和重连

3. **帧缓冲管理器** (`src/utils/frame_buffer.py`)
   - 为每个视频流维护帧缓冲
   - 支持多任务共享同一缓冲
   - 内存使用优化

4. **分析处理器池** (`src/processors/processor_pool.py`)
   - 管理各类分析处理器实例
   - 处理器复用和资源回收
   - 并发执行控制

### 4.3 数据流设计

#### 单任务处理流程
```
1. 接收API请求 → 2. 参数验证 → 3. 任务创建 → 4. 视频流获取
        ↓              ↓           ↓            ↓
5. 帧提取 → 6. 算法处理 → 7. 结果聚合 → 8. 响应返回
```

#### 并发任务处理流程
```
多个API请求 → 任务队列 → 调度器分配 → 流管理器 → 共享帧缓冲
     ↓           ↓        ↓          ↓         ↓
  任务标识 → 优先级排序 → 资源分配 → 连接复用 → 并行处理
```

### 4.4 资源管理策略

#### 视频流连接管理
- **连接池**: 最大5个并发视频流连接
- **连接复用**: 同一视频流的多个任务共享连接
- **超时控制**: 连接超时30秒，自动重连
- **资源清理**: 无任务时自动释放连接

#### 内存管理
- **帧缓冲**: 每个视频流最多缓存100帧
- **缓冲共享**: 同一流的多任务共享帧缓冲
- **内存限制**: 总内存使用不超过4GB
- **垃圾回收**: 定期清理无用缓冲

#### 计算资源管理
- **处理器池**: 每种分析类型维护2-3个处理器实例
- **任务队列**: 使用asyncio.Queue管理待处理任务
- **负载均衡**: 根据处理器负载分配任务
- **超时控制**: 单个分析任务最大执行时间限制

### 4.5 配置系统设计

```yaml
# config/app_config.yaml
server:
  host: "0.0.0.0"
  port: 8000
  debug: false

# 并发控制配置
concurrency:
  max_concurrent_streams: 5        # 最大并发视频流数
  max_tasks_per_stream: 3         # 每个流最大并发任务数
  task_queue_size: 50             # 任务队列大小
  processor_pool_size: 6          # 处理器池大小

# 分析器配置
analyzers:
  water_level:
    enabled: true
    default_method: "transparency"
    max_analysis_duration: 300
    processor_instances: 2        # 处理器实例数

  flow_speed:
    enabled: true
    default_method: "otv"
    max_analysis_duration: 600
    processor_instances: 2

  anomaly:
    enabled: true
    model_path: "models/yolo11n.pt"
    confidence_threshold: 0.5
    processor_instances: 2

# 视频流处理配置
video_processing:
  supported_protocols: ["rtmp", "rtsp", "http"]
  connection_timeout: 30
  reconnect_attempts: 3
  frame_buffer_size: 100
  frame_extraction_interval: 1    # 帧提取间隔(秒)

# 资源管理配置
resource_management:
  max_memory_usage: "4GB"
  cleanup_interval: 300           # 资源清理间隔(秒)
  idle_connection_timeout: 600    # 空闲连接超时(秒)

logging:
  level: "INFO"
  file_path: "data/logs/app.log"
  max_file_size: "10MB"
```

## 5. 性能和安全设计

### 5.1 性能指标

#### 响应时间指标
- **水位识别**: <10秒（单任务），<15秒（并发时）
- **流速识别**: <60秒（单任务），<90秒（并发时）
- **异常检测**: <5秒（单任务），<8秒（并发时）

#### 并发能力指标
- **最大并发视频流**: 5个
- **每流最大并发任务**: 3个（水位+流速+异常）
- **总并发任务数**: 最多15个
- **任务队列容量**: 50个待处理任务

#### 资源使用指标
- **内存使用**: 峰值<4GB，平均<2GB
- **CPU使用**: 峰值<90%，平均<60%
- **网络带宽**: 每个视频流<10Mbps
- **磁盘使用**: 临时文件<1GB

#### 稳定性指标
- **系统可用性**: >99%
- **任务成功率**: >95%
- **平均故障恢复时间**: <30秒
- **连接重试成功率**: >90%

### 5.2 安全措施

#### 输入安全
- **参数验证**: 使用Pydantic严格验证所有输入参数
- **URL白名单**: 限制可访问的视频流域名和协议
- **文件大小限制**: 限制上传文件和缓存大小
- **注入防护**: 防止路径遍历和命令注入

#### 运行时安全
- **超时控制**: 所有操作都有超时限制
- **资源限制**: 限制内存、CPU和磁盘使用
- **错误处理**: 统一错误处理，不泄露敏感信息
- **日志安全**: 敏感信息脱敏记录

#### 网络安全
- **连接验证**: 验证视频流URL的有效性
- **协议限制**: 只支持安全的视频流协议
- **访问控制**: 可选的API密钥认证
- **速率限制**: 防止API滥用

### 5.3 监控指标

#### 业务指标
- **任务处理量**: 每分钟处理的任务数
- **任务成功率**: 按分析类型统计的成功率
- **平均处理时间**: 各类分析的平均耗时
- **并发任务数**: 实时并发任务统计

#### 系统指标
- **CPU使用率**: 实时CPU使用情况
- **内存使用率**: 实时内存使用情况
- **网络IO**: 视频流下载速率
- **磁盘IO**: 临时文件读写速率

#### 错误指标
- **错误率**: 按错误类型统计的错误率
- **超时率**: 任务超时的比例
- **连接失败率**: 视频流连接失败率
- **重试成功率**: 失败任务重试的成功率

#### 性能指标
- **响应时间分布**: P50, P90, P95, P99响应时间
- **吞吐量**: 每秒处理的请求数
- **队列长度**: 待处理任务队列长度
- **资源利用率**: 各类资源的利用效率
