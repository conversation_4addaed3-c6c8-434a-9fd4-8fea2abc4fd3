# VR项目技术设计文档

本文档详细描述VR视频分析系统的技术架构、API设计和可复用组件分析。

## 1. 系统架构设计

### 1.1 整体架构

```
外部调用系统 → FastAPI服务 → 分析处理器 → 算法引擎
     ↓            ↓           ↓          ↓
  视频流URL → API接口验证 → 视频流处理 → 分析结果
```

**设计原则**：
- 单体应用架构，不是微服务
- 专注视频分析，不管理数据库
- 纯API服务，不包含客户端
- 所有输入都是视频流URL

### 1.2 技术栈选择

| 组件 | 技术选择 | 理由 |
|------|---------|------|
| Web框架 | FastAPI | 高性能异步框架，自动API文档 |
| Python版本 | 3.10+ | 现代特性支持，类型注解 |
| 视觉处理 | OpenCV | 成熟的图像视频处理库 |
| 数值计算 | NumPy | 高效数组计算 |
| 流速算法 | OpenPIV | 专业PIV算法库 |
| 异常检测 | YOLO | 成熟的目标检测模型 |
| 配置管理 | YAML + Pydantic | 人类可读 + 类型验证 |
| 测试框架 | pytest | 功能强大的测试工具 |

### 1.3 模块设计

```
src/
├── api/                    # API接口层
│   ├── analyze.py         # 分析接口
│   └── health.py          # 健康检查
├── services/              # 业务服务层
│   └── analyzer_service.py # 分析服务协调器
├── processors/            # 分析处理器层
│   ├── water_level/       # 水位识别
│   ├── flow_speed/        # 流速识别
│   └── anomaly/           # 异常检测
├── models/                # 数据模型
│   └── schemas.py         # API数据模型
├── utils/                 # 工具层
│   ├── video_utils.py     # 视频处理
│   └── logger.py          # 日志管理
└── config/                # 配置层
    └── config_manager.py  # 配置管理器
```

## 2. API接口设计

### 2.1 核心分析接口

#### 水位识别接口
```
POST /api/analyze/water-level
```

**请求格式**：
```json
{
    "video_url": "rtmp://example.com/stream",
    "parameters": {
        "method": "transparency",           // 检测方法
        "analysis_duration": 30,           // 分析时长(秒)
        "roi": {                          // 感兴趣区域(可选)
            "x": 100, "y": 100, "width": 500, "height": 300
        }
    }
}
```

**响应格式**：
```json
{
    "task_id": "wl_20240101_123456",
    "status": "completed",
    "result": {
        "water_level": {
            "depth_cm": 125.5,
            "confidence": 0.95
        },
        "method_used": "transparency",
        "processing_time": 2.5
    },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 流速识别接口
```
POST /api/analyze/flow-speed
```

**请求格式**：
```json
{
    "video_url": "rtmp://example.com/stream",
    "parameters": {
        "method": "otv",                   // 分析方法: otv/piv
        "analysis_duration": 60,          // 分析时长(秒)
        "roi": {                          // 分析区域
            "points": [                   // 多边形顶点
                {"x": 100, "y": 100},
                {"x": 400, "y": 100},
                {"x": 400, "y": 300},
                {"x": 100, "y": 300}
            ]
        },
        "calibration": {
            "pixel_to_meter": 0.01        // 像素到米转换比例
        }
    }
}
```

**响应格式**：
```json
{
    "task_id": "fs_20240101_123456",
    "status": "completed",
    "result": {
        "flow_speed": {
            "average_speed_ms": 0.85,
            "max_speed_ms": 1.2,
            "flow_direction": 92.5,
            "confidence": 0.88
        },
        "method_used": "otv",
        "processing_time": 45.2
    },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 异常检测接口
```
POST /api/analyze/anomaly
```

**请求格式**：
```json
{
    "video_url": "rtmp://example.com/stream",
    "parameters": {
        "detection_targets": ["person", "boat", "vehicle"],
        "analysis_duration": 30,
        "confidence_threshold": 0.5
    }
}
```

**响应格式**：
```json
{
    "task_id": "ad_20240101_123456",
    "status": "completed",
    "result": {
        "anomalies_detected": true,
        "detections": [
            {
                "object_type": "person",
                "confidence": 0.92,
                "bounding_box": {"x": 150, "y": 200, "width": 80, "height": 180},
                "timestamp": "2024-01-01T12:00:15Z"
            }
        ],
        "risk_level": "medium"
    },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### 2.2 系统接口

#### 健康检查
```
GET /api/health
```

**响应格式**：
```json
{
    "status": "healthy",
    "version": "1.0.0",
    "services": {
        "water_level": "active",
        "flow_speed": "active",
        "anomaly": "active"
    },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 配置查询
```
GET /api/config
```

**响应格式**：
```json
{
    "analyzers": {
        "water_level": {
            "enabled": true,
            "default_method": "transparency",
            "available_methods": ["transparency", "gradient", "hough", "color_threshold"]
        },
        "flow_speed": {
            "enabled": true,
            "default_method": "otv",
            "available_methods": ["otv", "piv"]
        },
        "anomaly": {
            "enabled": true,
            "model_path": "models/yolo11n.pt",
            "supported_targets": ["person", "boat", "vehicle"]
        }
    }
}
```

### 2.3 错误处理

**错误响应格式**：
```json
{
    "error": {
        "code": "INVALID_VIDEO_URL",
        "message": "无法访问指定的视频流URL",
        "details": {
            "url": "rtmp://example.com/stream",
            "error_type": "connection_timeout"
        }
    },
    "task_id": "wl_20240101_123456",
    "timestamp": "2024-01-01T12:00:00Z"
}
```

**常见错误码**：
- `INVALID_VIDEO_URL` - 视频流URL无效
- `VIDEO_STREAM_UNAVAILABLE` - 视频流不可访问
- `ANALYSIS_TIMEOUT` - 分析超时
- `INVALID_PARAMETERS` - 参数无效
- `INTERNAL_ERROR` - 内部错误

## 3. 可复用组件分析

### 3.1 水位识别项目组件

#### 高价值组件 ⭐⭐⭐⭐⭐
1. **配置管理系统** (`src/config/config_utils.py`)
   - ConfigManager单例模式
   - YAML配置加载和验证
   - 运行时配置更新

2. **水位检测算法库**
   - `transparency_utils.py` - 透明度检测（最可靠）
   - `gradient_utils.py` - 梯度检测
   - `hough_utils.py` - 霍夫变换
   - `color_threshold_utils.py` - 颜色阈值

3. **图像预处理工具** (`image_preprocessing_utils.py`)
   - ROI区域处理
   - 图像标准化
   - 坐标转换

4. **水尺识别系统**
   - `water_rule_utils.py` - 水尺检测
   - `shape_recognition_utils.py` - E字符识别

**复用策略**: 完整移植到`src/processors/water_level/`模块

### 3.2 流速识别项目组件

#### 中等价值组件 ⭐⭐⭐
1. **视频处理工具** (`src/utils/video_trimmer.py`)
   - 视频时间段裁剪
   - ROI区域提取
   - 多边形ROI支持

2. **日志管理系统** (`src/utils/logging_utils.py`)
   - 统一日志配置
   - 多级别输出

3. **OTV/PIV算法框架**
   - `opencv_otv_analyzer.py` - 光流法
   - `openpiv_piv_analyzer.py` - PIV算法

**复用策略**: 视频处理工具整合到`src/utils/video_utils.py`，算法需要优化后使用

### 3.3 CV异常检测项目组件

#### 高价值组件 ⭐⭐⭐⭐⭐
1. **FastAPI应用架构**
   - 完整的Web API框架
   - 异步请求处理
   - 生命周期管理

2. **异步处理框架**
   - `stream_processor.py` - 流处理器
   - `frame_buffer.py` - 帧缓冲管理

3. **YOLO模型集成** (`models/yolo_model.py`)
   - 模型加载和推理
   - 结果后处理

**复用策略**: FastAPI架构作为基础，YOLO集成用于异常检测

### 3.4 整合优先级

**第一优先级**：
1. FastAPI应用架构 (CV项目)
2. 配置管理系统 (水位项目)
3. 水位检测算法 (水位项目)

**第二优先级**：
1. 视频处理工具 (流速项目)
2. YOLO异常检测 (CV项目)
3. 日志管理系统 (流速项目)

**第三优先级**：
1. 流速识别算法 (流速项目，需优化)
2. 异步处理框架 (CV项目)

## 4. 数据流设计

### 4.1 分析请求处理流程

```
1. 接收API请求 → 2. 参数验证 → 3. 视频流连接 → 4. 帧提取
        ↓              ↓           ↓            ↓
5. 算法处理 → 6. 结果聚合 → 7. 响应格式化 → 8. 返回结果
```

### 4.2 视频流处理策略

1. **连接管理**: 支持RTMP/RTSP/HTTP视频流
2. **帧提取**: 按配置的帧率提取关键帧
3. **缓冲管理**: 维护固定大小的帧缓冲区
4. **异步处理**: 使用异步任务处理分析请求
5. **资源清理**: 分析完成后及时释放资源

### 4.3 配置系统设计

```yaml
# config/app_config.yaml
server:
  host: "0.0.0.0"
  port: 8000
  debug: false

analyzers:
  water_level:
    enabled: true
    default_method: "transparency"
    max_analysis_duration: 300  # 最大分析时长(秒)
    
  flow_speed:
    enabled: true
    default_method: "otv"
    max_analysis_duration: 600
    
  anomaly:
    enabled: true
    model_path: "models/yolo11n.pt"
    confidence_threshold: 0.5

video_processing:
  supported_protocols: ["rtmp", "rtsp", "http"]
  connection_timeout: 30
  frame_buffer_size: 100
  max_concurrent_streams: 5

logging:
  level: "INFO"
  file_path: "data/logs/app.log"
  max_file_size: "10MB"
```

## 5. 性能和安全设计

### 5.1 性能指标
- **响应时间**: 水位识别<10秒，流速识别<60秒，异常检测<5秒
- **并发能力**: 支持5个并发分析任务
- **内存使用**: 峰值<4GB
- **CPU使用**: 平均<80%

### 5.2 安全措施
- **输入验证**: 严格的参数验证
- **URL白名单**: 限制可访问的视频流域名
- **超时控制**: 防止长时间占用资源
- **错误处理**: 不泄露敏感信息

### 5.3 监控指标
- 分析任务成功率
- 平均处理时间
- 系统资源使用率
- 错误类型统计
