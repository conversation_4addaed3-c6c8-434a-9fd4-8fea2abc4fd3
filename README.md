# VR - 视频分析系统

## 项目概要
这是一个部署在站端的视频分析服务，通过API接口对外提供视频流分析能力。支持三种核心分析功能：水位识别、流速识别、异常人为干扰事件识别。

**项目定位**：
- 纯后端API服务，不包含客户端界面
- 接收视频流URL作为输入，返回分析结果
- 不管理数据库，专注于视频分析算法
- 由其他系统调用API接口获取分析结果

## 技术栈

### 核心框架
- **FastAPI** - 高性能异步Web框架
- **Python 3.10+** - 主要开发语言
- **Uvicorn** - ASGI服务器

### 计算机视觉
- **OpenCV** - 图像和视频处理
- **NumPy** - 数值计算
- **OpenPIV** - 流速识别算法
- **YOLO** - 异常检测模型

### 配置管理
- **YAML** - 配置文件格式
- **Pydantic** - 数据验证

### 开发工具
- **pytest** - 单元测试
- **logging** - 日志管理

## 系统架构

```
外部系统 → API接口 → 业务服务 → 分析处理器 → 算法引擎
   ↓         ↓        ↓         ↓         ↓
视频流URL → 参数验证 → 任务调度 → 视频处理 → 结果返回
```

### 核心模块
- **API层**: RESTful接口，接收分析请求
- **服务层**: 业务逻辑，任务调度和管理
- **处理器层**: 三大分析功能的具体实现
- **工具层**: 视频处理、配置管理等通用功能

## 目录结构
```
VR/
├── src/                    # 源代码目录
│   ├── api/               # API接口层
│   │   ├── __init__.py
│   │   ├── analyze.py     # 分析接口
│   │   └── health.py      # 健康检查接口
│   ├── services/          # 服务层
│   │   ├── __init__.py
│   │   └── analyzer_service.py    # 分析服务
│   ├── processors/        # 处理器层
│   │   ├── __init__.py
│   │   ├── water_level/   # 水位识别模块
│   │   ├── flow_speed/    # 流速识别模块
│   │   └── anomaly/       # 异常检测模块
│   ├── models/            # 数据模型
│   │   ├── __init__.py
│   │   └── schemas.py     # API数据模型
│   ├── utils/             # 工具函数
│   │   ├── __init__.py
│   │   ├── video_utils.py # 视频处理工具
│   │   └── logger.py      # 日志工具
│   └── config/            # 配置管理
│       ├── __init__.py
│       └── config_manager.py
├── config/                # 配置文件目录
│   └── app_config.yaml    # 应用配置
├── tests/                 # 测试文件目录
│   ├── __init__.py
│   ├── test_api/         # API测试
│   ├── test_processors/  # 处理器测试
│   └── test_utils/       # 工具函数测试
├── .augment/docs/         # 项目文档
├── data/                  # 数据目录
│   ├── temp/             # 临时文件
│   └── logs/             # 日志文件
├── models/               # AI模型文件
├── main.py              # 应用入口
├── requirements.txt     # Python依赖
└── README.md           # 项目说明
```

## 核心功能

### 1. 水位识别
- 透明度检测法（推荐）
- 梯度检测法
- 霍夫变换法
- 颜色阈值法
- 自动水尺识别和标定

### 2. 流速识别
- OTV光流法
- PIV粒子图像测速
- 自适应PIV算法

### 3. 异常检测
- 基于YOLO的目标检测
- 异常人为干扰事件识别

## API接口

### 核心分析接口
```
POST /api/analyze/water-level    # 水位识别
POST /api/analyze/flow-speed     # 流速识别
POST /api/analyze/anomaly        # 异常检测
```

### 系统接口
```
GET  /api/health                 # 健康检查
GET  /api/config                 # 获取配置
```

**输入**: 所有分析接口都接收视频流URL作为输入
**输出**: 返回JSON格式的分析结果

## 快速开始

### 环境要求
- Python 3.10+
- OpenCV 4.5+
- 4GB+ 内存

### 安装和运行
```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python main.py

# 或使用uvicorn
uvicorn main:app --host 0.0.0.0 --port 8000
```

### 测试API
```bash
# 健康检查
curl http://localhost:8000/api/health

# 水位识别示例
curl -X POST http://localhost:8000/api/analyze/water-level \
  -H "Content-Type: application/json" \
  -d '{"video_url": "rtmp://example.com/stream"}'
```

### 运行测试
```bash
pytest tests/ -v
```

## 文档

- **技术设计**: `.augment/docs/technical_design.md`
- **实施计划**: `.augment/docs/implementation_plan.md`