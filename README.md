# VR - 视频分析系统

## 项目概要
这是一个部署在站端的视频分析程序，站端会有多路不同的摄像头的视频流，具体分析任务、频次都各不相同。具体的分析任务包括水位识别、流速识别、异常人为干扰事件识别。这个项目的核心任务是对外提供分析接口进行具体的分析任务，而具体视频流的处理，结果的处理等更大范围的基础工作由其他项目完成。

## 技术栈

### 后端框架
- **FastAPI** - 高性能异步Web框架，提供RESTful API接口
- **Python 3.10+** - 主要开发语言
- **Uvicorn** - ASGI服务器

### 计算机视觉
- **OpenCV** - 图像和视频处理核心库
- **NumPy** - 数值计算和数组操作
- **OpenPIV** - 粒子图像测速算法库（流速识别）
- **YOLO** - 目标检测模型（异常检测）

### 配置和数据管理
- **YAML** - 配置文件格式
- **Pydantic** - 数据验证和设置管理
- **SQLite/MySQL** - 可选的数据库支持

### 开发工具
- **pytest** - 单元测试框架
- **logging** - 日志管理
- **asyncio** - 异步编程支持

## 项目架构

### 分层架构设计
```
┌─────────────────┐
│   API 接口层     │ (src/api/)
├─────────────────┤
│   服务层        │ (src/services/)
├─────────────────┤
│   处理器层      │ (src/processors/)
├─────────────────┤
│   模型层        │ (src/models/)
├─────────────────┤
│   工具层        │ (src/utils/)
├─────────────────┤
│   配置层        │ (src/config/)
└─────────────────┘
```

### 核心组件
- **API接口层**: 对外提供RESTful API接口
- **服务层**: 业务逻辑协调和管理
- **处理器层**: 具体的分析算法实现
  - 水位识别处理器
  - 流速识别处理器
  - 异常检测处理器
- **模型层**: 数据模型和验证
- **工具层**: 通用工具函数
- **配置层**: 配置管理和加载

## 目录结构
```
VR/
├── src/                    # 源代码目录
│   ├── api/               # API接口层
│   │   ├── __init__.py
│   │   ├── analyze.py     # 分析接口
│   │   ├── stream.py      # 视频流接口
│   │   └── config.py      # 配置接口
│   ├── services/          # 服务层
│   │   ├── __init__.py
│   │   ├── analyzer_service.py    # 分析服务
│   │   └── stream_service.py      # 视频流服务
│   ├── processors/        # 处理器层
│   │   ├── __init__.py
│   │   ├── water_level/   # 水位识别模块
│   │   ├── flow_speed/    # 流速识别模块
│   │   └── anomaly/       # 异常检测模块
│   ├── models/            # 数据模型
│   │   ├── __init__.py
│   │   ├── requests.py    # 请求模型
│   │   └── responses.py   # 响应模型
│   ├── utils/             # 工具函数
│   │   ├── __init__.py
│   │   ├── image_utils.py # 图像处理工具
│   │   ├── video_utils.py # 视频处理工具
│   │   └── logger.py      # 日志工具
│   └── config/            # 配置管理
│       ├── __init__.py
│       └── config_manager.py
├── config/                # 配置文件目录
│   ├── app_config.yaml    # 应用配置
│   ├── water_level.yaml   # 水位识别配置
│   ├── flow_speed.yaml    # 流速识别配置
│   └── anomaly.yaml       # 异常检测配置
├── tests/                 # 测试文件目录
│   ├── __init__.py
│   ├── test_api/         # API测试
│   ├── test_processors/  # 处理器测试
│   └── test_utils/       # 工具函数测试
├── .augment/docs/         # 项目文档
├── data/                  # 数据目录
│   ├── input/            # 输入数据
│   ├── output/           # 输出结果
│   └── temp/             # 临时文件
├── models/               # AI模型文件
├── main.py              # 应用入口
├── requirements.txt     # Python依赖
└── README.md           # 项目说明
```

## 核心功能模块

### 1. 水位识别 (Water Level Detection)
- **透明度检测法**: 基于水面透明度特征的检测算法
- **梯度检测法**: 基于像素强度梯度的检测算法
- **霍夫变换法**: 基于直线检测的水位识别
- **颜色阈值法**: 基于HSV颜色空间的水面检测
- **水尺识别**: 自动检测水尺并建立像素-厘米转换关系

### 2. 流速识别 (Flow Speed Detection)
- **OTV算法**: 基于OpenCV光流法的速度计算
- **PIV算法**: 基于粒子图像测速的流速分析
- **自适应PIV**: 具有自适应窗口大小的高级PIV算法
- **STIV算法**: 时空图像测速方法

### 3. 异常检测 (Anomaly Detection)
- **目标检测**: 基于YOLO模型的多目标检测
- **行为分析**: 异常人为干扰事件识别
- **实时监控**: 多视频流并发处理
- **事件报告**: 异常事件的自动报告和记录

## API接口设计

### 分析接口
- `POST /api/analyze/water-level` - 水位识别分析
- `POST /api/analyze/flow-speed` - 流速识别分析
- `POST /api/analyze/anomaly` - 异常检测分析
- `POST /api/analyze/batch` - 批量分析接口

### 视频流接口
- `POST /api/stream/start` - 启动视频流分析
- `POST /api/stream/stop` - 停止视频流分析
- `GET /api/stream/status` - 查询视频流状态

### 配置接口
- `GET /api/config` - 获取配置信息
- `PUT /api/config` - 更新配置信息
- `POST /api/config/reload` - 重新加载配置

## 开发指南

### 环境要求
- Python 3.10+
- OpenCV 4.5+
- 足够的内存用于视频处理
- GPU支持（可选，用于加速AI模型推理）

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行应用
```bash
# 开发模式
python main.py --config config/app_config.yaml --debug

# 生产模式
uvicorn main:app --host 0.0.0.0 --port 8000
```

### 运行测试
```bash
# 运行所有测试
pytest tests/ -v

# 运行特定模块测试
pytest tests/test_processors/ -v
```